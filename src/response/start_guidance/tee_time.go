package start_guidance

import "mi-restful-api/model/start_guidance"

type PlayerTeeResp struct {
	Code int                           `json:"code"`
	Msg  string                        `json:"msg"`
	Data *start_guidance.PlayerTeeTime `json:"data"`
}

type TeeTimeResp struct {
	Code int                          `json:"code"`
	Msg  string                       `json:"msg"`
	Data *start_guidance.TeeTimesData `json:"data"`
}

type CartInfoResp struct {
	Msg  string                         `json:"message"`
	Code int                            `json:"code"`
	Data *start_guidance.LockerCartInfo `json:"data"`
}
