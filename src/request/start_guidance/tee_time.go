package start_guidance

type CartInfoReq struct {
	LockerNo int `json:"locker_no" form:"locker_no" binding:"required,gte=0"` // LockerNo
}

type TeeTimePersonalReq struct {
	LockerNo int `json:"locker_no" form:"locker_no" binding:"required,gte=0"` // LockerNo
}

type TeeTimeReq struct {
	From  string `json:"from" form:"from" binding:"required"`         // from HH:MM
	Limit int    `json:"limit" form:"limit" binding:"required,gte=0"` //  limit >=0
}
