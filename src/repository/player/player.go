package player

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/configs"
	"mi-restful-api/model"
	"mi-restful-api/model/compe"
	"mi-restful-api/model/player"
	"mi-restful-api/model/tee"
	request "mi-restful-api/request/compe"
	playerResp "mi-restful-api/response/player"
	"mi-restful-api/utils/dynamo"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"gorm.io/gorm"
)

type PlayerRepo struct {
	ConnMySQLMncdb *gorm.DB
}

func ListCompePlayers(officeKey string, compeNo int) ([]compe.CompePlayer, error) {

	input := &dynamodb.QueryInput{TableName: aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		KeyConditionExpression: aws.String("partition_key = :pk AND begins_with(sort_key, :prefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":pk":     &types.AttributeValueMemberS{Value: "online-compe_" + strconv.Itoa(compeNo)},
			":prefix": &types.AttributeValueMemberS{Value: "online-compe_player_" + officeKey + "_"},
		},
	}

	result, err := dynamo.Query(input)
	if err != nil {
		slog.Error("ListCompePlayers dynamodb query error", "err", err, "officeKey", officeKey, "compeNo", compeNo)
		return nil, err
	}
	if result.Items == nil {
		slog.Error("ListCompePlayers", "err", "no items", "officeKey", officeKey, "compeNo", compeNo)
		return nil, nil
	}

	var compePlayers []compe.CompePlayer
	for _, item := range result.Items {
		var compePlayer compe.CompePlayer
		err = attributevalue.UnmarshalMap(item, &compePlayer)
		if err != nil {
			slog.Error("operation list office old compe", "err", err)
			continue
		}
		compePlayers = append(compePlayers, compePlayer)
	}
	return compePlayers, nil

}

func GetCompePlayer(officeKey string, compeNo int, playDate string, cartNo int, playerNo int) (*compe.CompePlayer, error) {
	slog.Info("GetCompePlayer", "officeKey", officeKey, "compeNo", compeNo, "playDate", playDate, "cartNo", cartNo, "playerNo", playerNo)
	pk := "online-compe_" + strconv.Itoa(compeNo)
	sk := "online-compe_player_" + officeKey + "_" + playDate + "_" + strconv.Itoa(cartNo) + "_" + strconv.Itoa(playerNo)
	key := map[string]types.AttributeValue{
		"partition_key": &types.AttributeValueMemberS{Value: pk},
		"sort_key":      &types.AttributeValueMemberS{Value: sk},
	}

	result, err := dynamo.GetItem(configs.GetDynamodbCompeConfig().CompeTableName, key)
	if err != nil {
		slog.Error("GetCompePlayer dynamodb get error", "err", err, "officeKey", officeKey, "compeNo", compeNo, "playDate", playDate, "playerNo", playerNo, "cartNo", cartNo)
		return nil, err
	}
	if result.Item == nil {
		slog.Error("GetCompePlayer dynamodb get error", "err", "no item", "officeKey", officeKey, "compeNo", compeNo, "playDate", playDate, "playerNo", playerNo, "cartNo", cartNo)
		return nil, nil
	}

	var compePlayer compe.CompePlayer
	err = attributevalue.UnmarshalMap(result.Item, &compePlayer)
	if err != nil {
		slog.Error("GetCompePlayer dynamodb unmarshal error", "err", err, "officeKey", officeKey, "compeNo", compeNo, "playDate", playDate, "playerNo", playerNo, "cartNo", cartNo)
		return nil, err
	}

	return &compePlayer, nil

}

func UpdateJoinedPlayers(compePlayers []compe.CompePlayer) (bool, error) {
	slog.Info("UpdateJoinedPlayers", "compePlayers", compePlayers)
	compeTableConfig := configs.GetDynamodbCompeConfig()
	for _, compePlayer := range compePlayers {
		item, err := attributevalue.MarshalMap(compePlayer.Details)
		if err != nil {
			slog.Error("update compe player error", "err", err, "params", item)
			return false, err
		}

		// update compe player
		input := &dynamodb.UpdateItemInput{
			TableName: aws.String(compeTableConfig.CompeTableName),
			Key: map[string]types.AttributeValue{
				"partition_key": &types.AttributeValueMemberS{Value: compePlayer.PartitionKey},
				"sort_key":      &types.AttributeValueMemberS{Value: compePlayer.SortKey},
			},
			UpdateExpression: aws.String("SET details = :details, expiration_time = :expiration_time, updated_at = :updated_at"),
			ExpressionAttributeValues: map[string]types.AttributeValue{
				":details":         &types.AttributeValueMemberM{Value: item},
				":expiration_time": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", compePlayer.ExpirationTime)},
				":updated_at":      &types.AttributeValueMemberS{Value: compePlayer.UpdatedAt.Format(time.RFC3339)},
			},
			ReturnValues: types.ReturnValueAllNew,
		}

		_, err = dynamo.UpdateItem(input)

		if err != nil {
			slog.Error("update compe player error", "err", err, "params", input)
			return false, err
		}

		// update player compe
		playerCompe := player.PlayerCompe{
			PartitionKey: compePlayer.Details.OfficeKey,
			SortKey: "player_online-compe_" + strconv.Itoa(compePlayer.Details.PlayerNo) +
				"_" + strconv.Itoa(compePlayer.Details.CompeNo) +
				"_" + strconv.Itoa(compePlayer.Details.CartNo) +
				"_" + compePlayer.Details.PlayDate,
			Details: player.PlayerCompeDetails{
				PlayerNo:    compePlayer.Details.PlayerNo,
				PlayerName:  compePlayer.Details.PlayerName,
				PlayDate:    compePlayer.Details.PlayDate,
				CartNo:      compePlayer.Details.CartNo,
				CompeNo:     compePlayer.Details.CompeNo,
				CourseIndex: compePlayer.Details.CourseIndex,
			},
			ExpirationTime: 0, // in seconds
			UpdatedAt:      time.Now(),
		}

		item, err = attributevalue.MarshalMap(playerCompe)
		if err != nil {
			slog.Error("update compe player MarshalMap error", "err", err, "params", playerCompe)
			return false, err
		}

		//delete cache []player.PlayerCompe if key exist
		redis := client.GetAppRedisClient()
		cacheKey := fmt.Sprintf("player_online-compe_%s_%d_%s", compePlayer.Details.OfficeKey, compePlayer.Details.PlayerNo, compePlayer.Details.PlayDate)
		_, err = redis.Del(context.Background(), cacheKey).Result()
		if err != nil {
			slog.Warn("Failed to delete player compes from cache", "err", err)
		}

		_, err = dynamo.PutItem(compeTableConfig.CompeTableName, item)

		if err != nil {
			slog.Error("update compe player error", "err", err, "params", item)
			return false, err
		}
	}

	return true, nil
}

func GetPlayerCompes(dateStr string, playerNo int, officeKey string) ([]player.PlayerCompe, error) {
	slog.Info("CompeRepo.GetPlayerCompe", "dateStr", dateStr, "playerNo", playerNo, "officeKey", officeKey)

	//TODO validate dateStr

	//check cache []player.PlayerCompe if key exist
	redis := client.GetAppRedisClient()
	cacheKey := fmt.Sprintf("player_online-compe_%s_%d_%s", officeKey, playerNo, dateStr)
	// Try to get from cache
	if cachedData, err := redis.Get(context.Background(), cacheKey).Result(); err == nil {
		var playerCompes []player.PlayerCompe
		if err := json.Unmarshal([]byte(cachedData), &playerCompes); err == nil {
			return playerCompes, nil
		}
		// If unmarshal fails, continue to DB query
		slog.Warn("Failed to unmarshal cached player compes", "err", err)
	}

	KeyConditionExpression := "partition_key = :office_key AND begins_with(sort_key, :prefix)"

	expressionAttributeValues := map[string]types.AttributeValue{
		":office_key": &types.AttributeValueMemberS{Value: officeKey},
		":prefix":     &types.AttributeValueMemberS{Value: "player_online-compe_" + fmt.Sprint(playerNo)},
	}

	result, err := dynamo.QueryItem(context.Background(),
		configs.GetDynamodbCompeConfig().CompeTableName,
		KeyConditionExpression,
		expressionAttributeValues,
		false)

	if err != nil {
		slog.Error("CompeRepo.GetPlayerCompe error", "dateStr", dateStr, "playerNo", playerNo, "officeKey", officeKey, "err", err)
		return nil, err
	}
	if result.Items == nil {
		slog.Warn("CompeRepo.GetPlayerCompe no item", "dateStr", dateStr, "playerNo", playerNo, "officeKey", officeKey)
		return nil, err
	}

	playerCompes := []player.PlayerCompe{}
	for _, item := range result.Items {
		var playerCompe player.PlayerCompe
		err = attributevalue.UnmarshalMap(item, &playerCompe)
		if err != nil {
			slog.Error("CompeRepo.GetPlayerCompe error", "dateStr", dateStr, "playerNo", playerNo, "officeKey", officeKey, "err", err)
			return nil, err
		}
		if playerCompe.Details.PlayDate != dateStr {
			continue
		}
		playerCompes = append(playerCompes, playerCompe)
	}

	// Marshal the playerCompes slice to JSON
	jsonData, err := json.Marshal(playerCompes)
	if err != nil {
		slog.Error("Failed to marshal player compes", "err", err)
		return nil, err
	}
	// Save to Redis
	err = redis.Set(context.Background(), cacheKey, jsonData, 1*time.Minute).Err()
	if err != nil {
		slog.Error("Failed to save player compes to cache", "err", err)
		return nil, err
	}

	return playerCompes, nil
}

func TeeSheetsByDateStr(dateStr string, officeKey string) ([]tee.TeeSheet, error) {

	slog.Info("play repo TeeSheet", "dateStr", dateStr, "officeKey", officeKey)

	input := &dynamodb.QueryInput{TableName: aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		KeyConditionExpression: aws.String("partition_key = :pk AND begins_with(sort_key, :prefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":pk":     &types.AttributeValueMemberS{Value: officeKey},
			":prefix": &types.AttributeValueMemberS{Value: "tee_sheet_" + dateStr},
		},
	}

	result, err := dynamo.Query(input)
	if err != nil {
		slog.Error("TeeSheet dynamodb query error", "err", err, "dateStr", dateStr, "officeKey", officeKey)
		return nil, err
	}
	if result.Items == nil {
		slog.Error("TeeSheet", "err", "no items", "dateStr", dateStr, "officeKey", officeKey)
		return nil, nil
	}

	var teeSheets []tee.TeeSheet
	for _, item := range result.Items {
		var teeSheet tee.TeeSheet
		err = attributevalue.UnmarshalMap(item, &teeSheet)
		if err != nil {
			slog.Error("operation list tee sheet", "err", err)
			continue
		}
		teeSheets = append(teeSheets, teeSheet)
	}
	return teeSheets, nil

}

func TeeSheetByDateStrAndCartNo(dateStr string, officeKey string, cartNo int, scheduledStartTime string) (*tee.TeeSheet, error) {

	slog.Info("play repo TeeSheet", "dateStr", dateStr, "officeKey", officeKey, "cartNo", cartNo, "scheduledStartTime", scheduledStartTime)

	// startTime := scheduledStartTime remove ":" or

	//get TeeSheet by get item
	sortKey := fmt.Sprintf("tee_sheet_%s_%d_%s", dateStr, cartNo, scheduledStartTime)

	key := map[string]types.AttributeValue{
		"partition_key": &types.AttributeValueMemberS{Value: officeKey},
		"sort_key":      &types.AttributeValueMemberS{Value: sortKey},
	}

	result, err := dynamo.GetItem(configs.GetDynamodbCompeConfig().CompeTableName, key)
	if err != nil {
		slog.Error("TeeSheet GetItem error", "err", err, "dateStr", dateStr, "officeKey", officeKey, "cartNo", cartNo, "scheduledStartTime", scheduledStartTime)
		return nil, err
	}

	if result.Item == nil {
		slog.Error("TeeSheet not found", "dateStr", dateStr, "officeKey", officeKey, "cartNo", cartNo, "scheduledStartTime", scheduledStartTime)
		return nil, fmt.Errorf("tee sheet not found")
	}

	var teeSheet tee.TeeSheet
	err = attributevalue.UnmarshalMap(result.Item, &teeSheet)
	if err != nil {
		slog.Error("TeeSheet unmarshal error", "err", err, "dateStr", dateStr, "officeKey", officeKey, "cartNo", cartNo, "scheduledStartTime", scheduledStartTime)
		return nil, err
	}

	return &teeSheet, nil
}

func UpdateTeeSheetByCartNoAndPlaydate(officeKey string, playDate string, cartNo int, scheduledStartTime string, teeSheet tee.TeeSheet) error {
	slog.Info("operation update tee sheet", "officeKey", officeKey, "playDate", playDate, "cartNo", cartNo, "scheduledStartTime", scheduledStartTime)
	// update TeeSheet
	sortKey := fmt.Sprintf("tee_sheet_%s_%d_%s", playDate, cartNo, scheduledStartTime)

	item, err := attributevalue.MarshalMap(teeSheet)
	if err != nil {
		slog.Error("update tee sheet MarshalMap error", "err", err, "params", teeSheet)
		return err
	}

	input := &dynamodb.UpdateItemInput{
		TableName: aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		Key: map[string]types.AttributeValue{
			"partition_key": &types.AttributeValueMemberS{Value: officeKey},
			"sort_key":      &types.AttributeValueMemberS{Value: sortKey},
		},
		UpdateExpression: aws.String("SET details = :details, updated_at = :updated_at"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":details":    &types.AttributeValueMemberM{Value: item["details"].(*types.AttributeValueMemberM).Value},
			":updated_at": &types.AttributeValueMemberS{Value: time.Now().Format(time.RFC3339)},
		},
		ReturnValues: types.ReturnValueAllNew,
	}

	_, err = dynamo.UpdateItem(input)
	if err != nil {
		slog.Error("update tee sheet error", "err", err, "officeKey", officeKey, "playDate", playDate, "cartNo", cartNo)
		return err
	}

	return nil
}

func SearchPlayerInfoByInternalApi(name string, birthday string) ([]player.PlayerInfo, error) {
	req := request.GolferSearchReq{
		Required: struct {
			GlidNo         string `json:"glid_no"`
			OfficeId       string `json:"office_id"`
			CourseIndex    string `json:"course_index"`
			PlayerName     string `json:"player_name"`
			PlayerBirthday string `json:"player_birthday"`
			SearchType     string `json:"search_type"`
		}{
			OfficeId:       "*",
			CourseIndex:    "0",
			PlayerName:     name,
			PlayerBirthday: birthday,
			SearchType:     "3",
		},
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	reqHttp, err := http.NewRequest("POST", configs.GetAPIConfig().GolferSearch, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	reqHttp.Header.Set("Content-Type", "application/json")
	reqHttp.Header.Set("x-marshal-i-api-token", configs.GetAPIConfig().GolferSearchToken)

	client := &http.Client{}
	resp, err := client.Do(reqHttp)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("GolferSearch Code: %d", resp.StatusCode)
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var golferSearchResp playerResp.GolferSearchResp
	err = json.Unmarshal(body, &golferSearchResp)
	if err != nil {
		return nil, err
	}

	joinedPlayer := make([]player.PlayerInfo, 0)
	for _, result := range golferSearchResp.Result {
		gender, err := strconv.Atoi(result.PlayerSex)
		if err != nil {
			gender = 0
		}
		joinedPlayer = append(joinedPlayer, player.PlayerInfo{
			GlidNo:     result.GlidNo,
			HdcpIndex:  result.HdcpIndex,
			PlayerName: result.PlayerName,
			Birthday:   result.PlayerBirthday,
			Gender:     gender,
		})
	}

	return joinedPlayer, nil
}

func SearchPlayerCourseRateByInteralApi(officeId string, player compe.CompePlayerDetails) (*playerResp.CourseRatingResult, error) {
	slog.Info("SearchPlayerCourseRateByInteralApi", "officeId", officeId, "player", player)

	if player.TeeId == nil {
		return nil, fmt.Errorf("tee id is nil")
	}

	courseIndex := 0

	if player.CourseIndex != nil {
		courseIndex = *player.CourseIndex
	}

	req := request.SearchCourseRatingReq{
		Required: struct {
			OfficeId    string `json:"office_id"`
			CourseIndex int    `json:"course_index"`
		}{
			OfficeId:    officeId,
			CourseIndex: courseIndex,
		},
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	reqHttp, err := http.NewRequest("POST", configs.GetAPIConfig().CourseRateSearch, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	reqHttp.Header.Set("Content-Type", "application/json")
	reqHttp.Header.Set("x-marshal-i-api-token", configs.GetAPIConfig().CourseRateSearchToken)

	client := &http.Client{}
	resp, err := client.Do(reqHttp)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("GolferSearch Code: %d", resp.StatusCode)
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var courseRatingSearchResp playerResp.CourseRatingSearchResp
	err = json.Unmarshal(body, &courseRatingSearchResp)
	if err != nil {
		return nil, err
	}

	if len(courseRatingSearchResp.Result) == 0 {
		return nil, fmt.Errorf("no course rating result")
	}

	courseRatingResults := []playerResp.CourseRatingResult{}

	for _, result := range courseRatingSearchResp.Result {
		if result.InTeeID == *player.TeeId &&
			(result.EndsAt == "" || strings.Replace(result.EndsAt, "-", "", -1) >= player.PlayDate) {
			courseRatingResults = append(courseRatingResults, result)
		}
	}

	if len(courseRatingResults) == 0 {
		return nil, fmt.Errorf("no course rating result after filtering")
	}

	sort.Slice(courseRatingResults, func(i, j int) bool {
		return courseRatingResults[i].StartsAt > courseRatingResults[j].StartsAt
	})

	return &courseRatingResults[0], nil
}

func SearchPlayerInfoByExternalApi(glidNo string, targetHdcpDate string) (player.PlayerInfo, error) {
	req := request.GetGolferDataReq{
		Required: struct {
			GlidNo         string `json:"glid_no"`
			TargetHdcpDate string `json:"target_hdcp_date"`
		}{
			GlidNo:         glidNo,
			TargetHdcpDate: targetHdcpDate,
		},
	}
	jsonData, err := json.Marshal(req)
	if err != nil {
		return player.PlayerInfo{}, err
	}

	reqHttp, err := http.NewRequest("POST", configs.GetAPIConfig().GetGolferData, bytes.NewBuffer(jsonData))
	if err != nil {
		return player.PlayerInfo{}, err
	}
	reqHttp.Header.Set("Content-Type", "application/json")
	reqHttp.Header.Set("Ggs-Api-Token", configs.GetAPIConfig().GetGolferDataToken)

	client := &http.Client{}
	resp, err := client.Do(reqHttp)
	if err != nil {
		return player.PlayerInfo{}, err
	}
	if resp.StatusCode != http.StatusOK {
		return player.PlayerInfo{}, fmt.Errorf("GetGolferData Code: %d", resp.StatusCode)
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return player.PlayerInfo{}, err
	}

	var golferSearchResp playerResp.GetGolferDataResp
	err = json.Unmarshal(body, &golferSearchResp)
	if err != nil {
		return player.PlayerInfo{}, err
	}

	joinedPlayer := player.PlayerInfo{
		GlidNo:       golferSearchResp.Result.GlidNo,
		HdcpIndex:    golferSearchResp.Result.TargetHdcpIndex, // use target hdcp index
		PlayerName:   golferSearchResp.Result.Name,
		Birthday:     golferSearchResp.Result.Birthday,
		Gender:       golferSearchResp.Result.Gender,
		HomeClubName: golferSearchResp.Result.HomeClubName,
	}

	return joinedPlayer, nil
}

func DeletePlayer(partitionKey string, sortKey string) error {
	input := &dynamodb.DeleteItemInput{
		TableName: aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		Key: map[string]types.AttributeValue{
			"partition_key": &types.AttributeValueMemberS{Value: partitionKey},
			"sort_key":      &types.AttributeValueMemberS{Value: sortKey},
		},
	}

	_, err := dynamo.DeleteItem(input)
	if err != nil {
		return err
	}

	return nil
}

func GetPlayersFromDynamo(dateStr string, officeKey string) ([]player.Player, error) {
	slog.Info("PlayerRepo.GetPlayers", "dateStr", dateStr, "officeKey", officeKey)
	//TODO add players cache into redis for 1min
	result, err := dynamo.QueryItem(context.Background(), configs.GetDynamodbCompeConfig().MashiaiTableName,
		"partition_key = :office_key AND begins_with(sort_key, :prefix) ", map[string]types.AttributeValue{
			":office_key": &types.AttributeValueMemberS{Value: officeKey},
			":prefix":     &types.AttributeValueMemberS{Value: "player_" + dateStr + "_"},
		}, true)
	if err != nil {
		slog.Error("PlayerRepo.GetPlayers error", "dateStr", dateStr, "officeKey", officeKey, "err", err)
		return nil, err
	}
	if result.Items == nil {
		slog.Warn("PlayerRepo.GetPlayers no item", "dateStr", dateStr, "officeKey", officeKey)
		return nil, err
	}
	players := []player.Player{}
	for _, item := range result.Items {
		var player player.Player
		err = attributevalue.UnmarshalMap(item, &player)
		if err != nil {
			slog.Error("PlayerRepo.GetPlayers unmarshal error", "userIdToken", dateStr, "officeKey", officeKey, "err", err)
			return nil, err
		}
		players = append(players, player)
	}
	return players, nil
}

func (r *PlayerRepo) GetOfficeByKey(officeKey string) (*model.Office, error) {

	var o model.Office
	err := r.ConnMySQLMncdb.Model(&model.Office{}).Where("office_key=? and deleted_at is null", officeKey).First(&o).Error
	if err != nil {
		slog.Error("GenerateAccessToken get office error:", "err", err)
		return nil, err
	}
	return &o, nil
}

func (r *PlayerRepo) GetPlayHistoryFromRDS(officeId int, cartNo int, playDate string) ([]model.PlayHistory, error) {

	data := []model.PlayHistory{}
	subQuery := r.ConnMySQLMncdb.Model(&model.PlayHistory{}).
		Select("start_time, cart_no, MAX(created_at) AS max_created_at").
		Where("office_id=? and play_history_date=? and cart_no=?", officeId, playDate, cartNo).
		Group("start_time, cart_no")

	err := r.ConnMySQLMncdb.Table("play_history AS ph").
		Joins("JOIN (?) AS sq ON ph.start_time = sq.start_time AND ph.cart_no = sq.cart_no AND ph.created_at = sq.max_created_at", subQuery).
		Where("ph.office_id=? and ph.play_history_date=? and ph.cart_no=?", officeId, playDate, cartNo).
		Select("ph.*").
		Find(&data).Error

	if err != nil {
		return nil, err
	}
	return data, nil
}
