package table_init

import (
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/model"
	"mi-restful-api/model/compe"
)

func InitTable() {
	db, err := client.GetDbClient()
	if err != nil {
		slog.Info("db init error")
		return
	}

	err = db.AutoMigrate(&model.Evaluation{})
	if err != nil {
		slog.Info("db init score error", "err", err.Error())
	}
	// 手动创建联合索引
	//err = db.Exec("CREATE INDEX idx_office_deleted ON comment (office_id, deleted_at)").Error
	//if err != nil {
	//	panic("failed to create index: " + err.Error())
	//}

	err = db.AutoMigrate(&model.Questionnaire{}, &model.Player{}, &model.Answer{}, &model.Caddy{})
	if err != nil {
		slog.Info("db init score error", "err", err.Error())
	}

	err = db.AutoMigrate(&model.Question{})
	if err != nil {
		slog.Info("db init score error", "err", err.Error())
	}

	err = db.AutoMigrate(&model.PlayerFeedback{})
	if err != nil {
		slog.Info("db init player_feedback error", "err", err.Error())
	}

	err = db.AutoMigrate(&model.QuesnaireSettings{})
	if err != nil {
		slog.Info("db init quesnaire_settings error", "err", err.Error())
	}

	err = db.AutoMigrate(&model.StartGuideSettings{})
	if err != nil {
		slog.Info("db init start_guide_settings error", "err", err.Error())
	}

	err = db.AutoMigrate(&compe.OnlineCompeIndex{})
	if err != nil {
		slog.Info("db init online_compe_index error", "err", err.Error())
	}

	err = db.AutoMigrate(&model.OfficeSettings{})
	if err != nil {
		slog.Info("db init office_settings error", "err", err.Error())
	}

	slog.Info("table init over ...")
	// 手动创建联合索引
	//err = db.Exec("CREATE INDEX idx_office_deleted ON question (office_id, deleted_at)").Error
	//if err != nil {
	//	panic("failed to create index: " + err.Error())
	//}
}
