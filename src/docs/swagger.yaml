basePath: /api
definitions:
  compe.Basic:
    properties:
      compe_name:
        description: コンペ名
        type: string
      compe_no:
        description: コンペNo
        minimum: 1
        type: integer
      duration:
        description: 開催日
        properties:
          from:
            description: ISO 8601 or date type
            type: string
          to:
            description: after from also should after now
            type: string
        required:
        - from
        - to
        type: object
      old_compe:
        properties:
          old_compe_no:
            type: string
          old_compe_office_key:
            type: string
          old_compe_start_time:
            type: string
        type: object
      organizer:
        description: 主催者
        type: string
      participation_fee:
        description: 参加料金 円
        type: integer
      promotional_image:
        description: 宣伝画像登録
        type: string
      target_office:
        description: 自コースのオフィスKEY
        type: string
      target_office_list:
        description: グループコース
        items:
          type: string
        type: array
      target_office_type:
        description: 0 自コース 1 グループコース
        type: integer
    required:
    - compe_name
    - compe_no
    - duration
    - organizer
    - participation_fee
    - promotional_image
    - target_office_type
    type: object
  compe.BasicResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー 500 内部エラー
        type: integer
      message:
        description: message メッセージ
        type: string
    type: object
  compe.BreakingNews:
    properties:
      player_no:
        type: string
      score:
        type: string
    type: object
  compe.CompeSetting:
    properties:
      entry_from_navi:
        description: ナビからエントリー 0 許可しない 1許可する
        type: integer
      prize_setting:
        description: 入賞設定
        items:
          properties:
            name:
              type: string
            order:
              additionalProperties:
                type: integer
              type: object
            setting:
              additionalProperties:
                type: integer
              type: object
            type:
              type: string
          required:
          - type
          type: object
        type: array
      ranking_aggregation:
        description: ランキング集計 0ホールを隠す 1最終3ホールを隠す 2最終6ホールを隠す 3最終9ホールを隠す
        type: integer
      round:
        description: 0.5ラウンド 1ラウンド 1.5ラウンド
        type: string
    required:
    - entry_from_navi
    - prize_setting
    - ranking_aggregation
    - round
    type: object
  compe.CompeTypeSetting:
    properties:
      handy:
        description: ハンディ optional item for 個人戦
        properties:
          distribution:
            description: 0 配信しない 1 配信する
            type: integer
          handicap:
            description: 使用ハンディキャップ
            properties:
              hdcp_allowance:
                description: HDCPの許容値 0-100 (%)
                maximum: 100
                minimum: 0
                type: integer
              hdcp_date:
                description: 時点のHDCPを使う
                type: string
              type:
                description: 0 HDCP Index(WHS) 1 プライベートハンディキャップ
                type: integer
            required:
            - hdcp_allowance
            - hdcp_date
            - type
            type: object
          net_computation_type:
            description: NET計算方法 0 HDCPナンバーで割り振り 1 按分方式
            type: integer
          ranking_order:
            additionalProperties:
              type: string
            description: 同点時の優先順位
            type: object
        required:
        - distribution
        - handicap
        - net_computation_type
        - ranking_order
        type: object
      peoria:
        description: ぺリア optional item for 個人戦
        properties:
          aggregation_method:
            description: 集計方法
            properties:
              type:
                description: 0 ペリア(6H)、1 新ペリア(12H)、2 新新ペリア(9H)
                type: integer
            required:
            - type
            type: object
          distribution:
            description: 0 配信しない 1 配信する
            type: integer
          handicap_upper_limit:
            description: ハンデ上限設定
            properties:
              men:
                description: men hdcp max
                type: integer
              women:
                description: women hdcp max
                type: integer
            type: object
          par_limit:
            description: 打数制限
            properties:
              par_n:
                description: '?'
                type: integer
              par_x:
                description: PAR×N+X
                type: integer
              type:
                description: 0 制限なし、1 PAR×2、2 PAR×2-1、3 PAR×3、4 PAR×3-1、　5  PAR+X、6
                  X、7 PAR×N+X　から選択。
                type: integer
            required:
            - type
            type: object
          ranking_order:
            additionalProperties:
              type: string
            description: 同点時の優先順位
            type: object
        required:
        - aggregation_method
        - distribution
        - handicap_upper_limit
        - par_limit
        - ranking_order
        type: object
      type:
        description: 0 team 1 個人戦
        type: integer
    required:
    - type
    type: object
  compe.DefaultCompeSetting:
    properties:
      entry_from_navi:
        description: ナビからエントリー 0 許可しない 1許可する
        type: integer
      ranking_aggregation:
        description: ランキング集計 0ホールを隠す 1最終3ホールを隠す 2最終6ホールを隠す 3最終9ホールを隠す
        type: integer
      round:
        type: string
    type: object
  compe.DefaultHandicap:
    properties:
      type:
        description: ハンディキャップSetting 0 HDCP Index(WHS) 1 フロント連携HDCP
        type: integer
    type: object
  compe.DefaultOtherSetting:
    properties:
      leadboard_change:
        description: リーダーボード切替
        properties:
          default:
            description: '?'
            type: string
          type:
            description: 0 しない 1 自由設定
            type: integer
        type: object
      marker_setting:
        description: マーカー設定 0 しない 1 マーカーあり
        type: integer
    type: object
  compe.HiddenHoleSetting:
    properties:
      course_index:
        description: コースインデックス
        type: string
      course_name:
        description: コース名
        type: string
      hidden_hole_index:
        description: 隠しホールインデックス
        items:
          type: integer
        type: array
    required:
    - course_index
    - course_name
    - hidden_hole_index
    type: object
  compe.Holes:
    properties:
      hole_index:
        type: string
      used_hdcp:
        type: string
      used_par:
        type: string
    type: object
  compe.LeaderBoardRankingResp:
    properties:
      code:
        type: integer
      data:
        properties:
          breaking_news:
            items:
              $ref: '#/definitions/compe.BreakingNews'
            type: array
          compe_no:
            type: string
          courses:
            items:
              $ref: '#/definitions/compe.ScoreCourses'
            type: array
          play_date:
            type: string
          rankings:
            items:
              $ref: '#/definitions/compe.PlayerRankings'
            type: array
          updated_at:
            type: string
        type: object
      message:
        type: string
    type: object
  compe.LeaderBoardRankingTypeResp:
    properties:
      code:
        type: integer
      data:
        properties:
          ranking_type:
            items:
              type: string
            type: array
        type: object
      message:
        type: string
    type: object
  compe.LeaderboardRankingShareKeyCreateReq:
    properties:
      compe_no:
        minimum: 1
        type: integer
      share_key:
        description: optinal size should be 6
        type: string
    required:
    - compe_no
    type: object
  compe.LeaderboardRankingShareKeyCreateResp:
    properties:
      code:
        type: integer
      data:
        properties:
          share_key:
            type: string
        type: object
      message:
        type: string
    type: object
  compe.LeaderboardRankingShareReq:
    properties:
      compe_no:
        minimum: 1
        type: integer
      email:
        type: string
      share_key:
        type: string
    required:
    - compe_no
    - email
    - share_key
    type: object
  compe.MergedCompe:
    properties:
      aggregation_types:
        description: 競技方法list
        items:
          type: string
        type: array
      compe_name:
        type: string
      compe_no:
        type: integer
      compe_type:
        description: 0 team (only for phase2) 1 個人戦
        type: integer
      duration:
        properties:
          from:
            description: 開催日 >= From
            type: string
          to:
            description: 開催日 <= To
            type: string
        type: object
      hidden_hole_setted:
        description: 隠しホール設定済み
        type: boolean
      is_front_system:
        description: is フロントシステム
        type: boolean
      joined_players_count:
        description: 参加人数
        type: integer
      participation_fee:
        description: 参加料金 円
        type: integer
      prize_condition_setted:
        description: 入賞条件設定済み
        type: boolean
      shared_key:
        description: 共有キー
        type: string
    type: object
  compe.OnlineCompeCreationReq:
    properties:
      basic:
        $ref: '#/definitions/compe.Basic'
      compe_setting:
        $ref: '#/definitions/compe.CompeSetting'
      compe_type_setting:
        $ref: '#/definitions/compe.CompeTypeSetting'
      other_setting:
        $ref: '#/definitions/compe.OtherSetting'
      private_setting:
        $ref: '#/definitions/compe.PrivateSetting'
    required:
    - basic
    - compe_setting
    - compe_type_setting
    - other_setting
    type: object
  compe.OnlineCompeDefaultSettingReq:
    properties:
      compe_setting:
        allOf:
        - $ref: '#/definitions/compe.DefaultCompeSetting'
        description: コンペ設定
      handicap:
        allOf:
        - $ref: '#/definitions/compe.DefaultHandicap'
        description: ハンディキャップSetting
      other_setting:
        allOf:
        - $ref: '#/definitions/compe.DefaultOtherSetting'
        description: その他設定
    type: object
  compe.OnlineCompeDefaultSettingResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー 500 内部エラー
        type: integer
      data:
        properties:
          compe_setting:
            $ref: '#/definitions/compe.DefaultCompeSetting'
          handicap:
            $ref: '#/definitions/compe.DefaultHandicap'
          other_setting:
            $ref: '#/definitions/compe.DefaultOtherSetting'
        type: object
      message:
        description: message メッセージ
        type: string
    type: object
  compe.OnlineCompeJoinedPlayersReq:
    properties:
      joined_players:
        items:
          $ref: '#/definitions/player.JoinedPlayer'
        minItems: 0
        type: array
    required:
    - joined_players
    type: object
  compe.OnlineCompeLatestNoResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー 500 内部エラー
        type: integer
      data:
        properties:
          compe_no:
            description: コンペNo
            type: integer
        type: object
      message:
        description: message メッセージ
        type: string
    type: object
  compe.OnlineCompeOfficeResp:
    properties:
      code:
        type: integer
      data:
        properties:
          compes:
            items:
              $ref: '#/definitions/compe.MergedCompe'
            type: array
        type: object
      message:
        type: string
    type: object
  compe.OnlineCompePlayerJoinReq:
    properties:
      birthday:
        type: string
      cart_no:
        type: integer
      compe_no:
        type: integer
      course_index:
        type: integer
      gender:
        description: １：男性、２：女性
        type: integer
      glid_no:
        type: string
      hdcp:
        type: string
      hdcp_index:
        type: string
      is_paid:
        type: boolean
      office_key:
        type: string
      play_date:
        type: string
      player_name:
        type: string
      player_no:
        minimum: 1
        type: integer
      team_class_type:
        type: integer
      tee_id:
        type: string
    required:
    - player_no
    type: object
  compe.OnlineCompeResp:
    properties:
      code:
        type: integer
      data:
        properties:
          basic:
            $ref: '#/definitions/compe.Basic'
          compe_setting:
            $ref: '#/definitions/compe.CompeSetting'
          compe_type_setting:
            $ref: '#/definitions/compe.CompeTypeSetting'
          other_setting:
            $ref: '#/definitions/compe.OtherSetting'
          private_setting:
            $ref: '#/definitions/compe.PrivateSetting'
        type: object
      message:
        type: string
    type: object
  compe.OnlineCompeUpdateReq:
    properties:
      basic:
        $ref: '#/definitions/compe.Basic'
      compe_setting:
        $ref: '#/definitions/compe.CompeSetting'
      compe_type_setting:
        $ref: '#/definitions/compe.CompeTypeSetting'
      other_setting:
        $ref: '#/definitions/compe.OtherSetting'
      private_setting:
        $ref: '#/definitions/compe.PrivateSetting'
    type: object
  compe.OtherSetting:
    properties:
      leadboard_change:
        description: リーダーボード切替
        properties:
          default:
            description: 'option : net, gross when 1  ; "" when  type is 0'
            type: string
          type:
            description: 0 しない 1 自由設定
            type: integer
        required:
        - default
        - type
        type: object
      marker_setting:
        description: マーカー設定 0 しない 1 マーカーあり
        type: integer
    required:
    - leadboard_change
    - marker_setting
    type: object
  compe.PlayerRankings:
    properties:
      course_hdcp:
        type: string
      course_index:
        type: string
      hdcp_index:
        type: string
      hole:
        type: string
      hole_index:
        type: string
      hole_number:
        type: string
      hole_score:
        items:
          $ref: '#/definitions/compe.RankingHoleScore'
        type: array
      input_hole_count:
        type: integer
      is_tied:
        type: integer
      order_net:
        type: string
      par_gross:
        type: integer
      par_net:
        type: number
      player_name:
        type: string
      player_no:
        type: string
      pos:
        type: string
      pos_net:
        type: string
      score_gross:
        type: integer
      score_net:
        type: number
    type: object
  compe.PrivateSetting:
    properties:
      course_setting:
        additionalProperties:
          type: string
        description: コース設定
        type: object
      hidden_hole:
        description: 隠しホール設定 list
        items:
          $ref: '#/definitions/compe.HiddenHoleSetting'
        type: array
    required:
    - course_setting
    - hidden_hole
    type: object
  compe.RankingHoleScore:
    properties:
      course_index:
        type: string
      hole_hdcp:
        type: integer
      hole_index:
        type: string
      hole_number:
        type: string
      score:
        type: string
      stroke:
        type: string
    type: object
  compe.ScoreCourses:
    properties:
      course_index:
        type: string
      course_name:
        type: string
      holes:
        items:
          $ref: '#/definitions/compe.Holes'
        type: array
      start_hole:
        type: string
    type: object
  compe.SearchPlayerInfoReq:
    properties:
      birthday:
        description: yyyy-MM-dd
        type: string
      glid_no:
        type: string
      hdcp_date:
        description: handy.handicap.hdcp_date  yyyy-MM-dd
        type: string
      player_name:
        type: string
      search_type:
        description: 'serach_type 1：internal api 2: external api 3: internal api +
          external api'
        type: integer
    required:
    - search_type
    type: object
  compe.UploadImgResp:
    properties:
      code:
        type: integer
      data:
        properties:
          url:
            type: string
        type: object
      message:
        type: string
    type: object
  course.Course:
    properties:
      course_index:
        type: string
      course_name:
        type: string
      holes:
        items:
          $ref: '#/definitions/course.Hole'
        type: array
      start_hole:
        type: string
    type: object
  course.CoursesResp:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/course.Course'
        type: array
      msg:
        type: string
    type: object
  course.Hole:
    properties:
      green_index:
        type: string
      hole_index:
        type: string
      used_hdcp:
        type: string
      used_par:
        type: string
    type: object
  mi-restful-api_response_player.PlayerCompe:
    properties:
      compe_basic:
        $ref: '#/definitions/compe.Basic'
      compe_name:
        type: string
      compe_no:
        type: integer
      compe_setting:
        $ref: '#/definitions/compe.CompeSetting'
      compe_type_setting:
        $ref: '#/definitions/compe.CompeTypeSetting'
      other_setting:
        $ref: '#/definitions/compe.OtherSetting'
      player:
        $ref: '#/definitions/player.JoinedPlayer'
    type: object
  player.JoinedPlayer:
    properties:
      birthday:
        description: eg 1942/10/13
        type: string
      cart_no:
        minimum: 1
        type: integer
      course_index:
        type: integer
      gender:
        type: integer
      glid_no:
        type: string
      hdcp:
        type: string
      hdcp_index:
        type: string
      is_paid:
        type: boolean
      office_key:
        type: string
      play_date:
        type: string
      player_name:
        type: string
      player_no:
        minimum: 1
        type: integer
      playing_hdcp:
        type: string
      team_class_type:
        type: integer
      tee_id:
        type: string
    required:
    - cart_no
    - player_no
    type: object
  player.JoinedPlayesResp:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/player.JoinedPlayer'
        type: array
      msg:
        type: string
    type: object
  player.PlayerCompeResp:
    properties:
      code:
        type: integer
      data:
        description: kv , key is compe no value is joined player
        items:
          $ref: '#/definitions/mi-restful-api_response_player.PlayerCompe'
        type: array
      msg:
        type: string
    type: object
  player.PlayerInfo:
    properties:
      birthday:
        type: string
      gender:
        type: integer
      glid_no:
        type: string
      hdcp_index:
        type: string
      home_club_name:
        type: string
      player_name:
        type: string
    type: object
  player.PlayerInfoResp:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/player.PlayerInfo'
        type: array
      msg:
        type: string
    type: object
  request.EvaluationPutReq:
    properties:
      content:
        description: content 評価文言
        maxLength: 200
        type: string
      id:
        description: id 評価段階
        type: integer
      score:
        description: score 評価配点
        type: integer
      stage:
        description: stage だんかい
        type: integer
    required:
    - content
    - score
    - stage
    type: object
  request.OfficeSettingsCreateReq:
    properties:
      card_reader_type:
        description: カードリード種類　0:なし　1:バーコードリード　2:ICカードリード
        maximum: 2
        minimum: 0
        type: integer
      enable_questionnaire:
        description: アンケート機能　0:無効　1:有効
        maximum: 1
        minimum: 0
        type: integer
      enable_self_score_print:
        description: セルフスコア印刷機能　0:無効　1:有効
        maximum: 1
        minimum: 0
        type: integer
      enable_start_guide:
        description: スタート案内機能　0:無効　1:有効
        maximum: 1
        minimum: 0
        type: integer
    required:
    - card_reader_type
    - enable_questionnaire
    - enable_self_score_print
    - enable_start_guide
    type: object
  request.QuesnaireSettingsCreateReq:
    properties:
      caddy_name_type:
        description: キャディの名前種類：1(キャディ)、2(コースアテンダント)
        maximum: 2
        minimum: 1
        type: integer
    required:
    - caddy_name_type
    type: object
  request.Question:
    properties:
      content:
        description: content 設問内容
        maxLength: 200
        type: string
      require:
        description: require 必須
        type: integer
      type:
        description: type 設問タイプ
        type: integer
    required:
    - content
    - require
    - type
    type: object
  request.QuestionnaireCreateReq:
    properties:
      caddy_id:
        description: caddy_id キャディID
        maxLength: 200
        type: string
      caddy_name:
        description: caddy_id キャディ名前
        maxLength: 200
        type: string
      cart_no:
        description: cart_no カート番号
        type: string
      feedback:
        description: feedback Custom Comment
        maxLength: 1100
        type: string
      feedback_caddy:
        description: キャディのfeedback Custom Comment
        maxLength: 1100
        type: string
      feedback_golf:
        description: ゴルフ場のfeedback Custom Comment
        maxLength: 1100
        type: string
      played_date:
        description: played_date プレイ時間
        maxLength: 200
        type: string
      player_id:
        description: player_id プレイヤーid
        maxLength: 200
        type: string
      player_name:
        description: player_name プレイヤー名
        maxLength: 200
        type: string
      serial:
        description: serial from operation-info (combine date and players sorted ids)
        type: string
      sort_key:
        description: sort_key dynamodb sort_key
        type: string
      start_course:
        description: start_course スタートコース
        maxLength: 200
        type: string
      start_time:
        description: start_time スタート時間
        maxLength: 200
        type: string
      survey:
        description: survey 設問
        items:
          $ref: '#/definitions/request.Survey'
        type: array
    required:
    - cart_no
    - played_date
    - player_id
    - player_name
    - survey
    type: object
  request.QuestionnaireIndexReq:
    properties:
      caddy_id:
        description: caddy_id キャディID
        type: integer
      end_date:
        description: end_date 終了時間
        type: string
      limit:
        description: limit １ページ当たり件数
        type: integer
      page:
        description: page ページ数 default 1 start
        type: integer
      start_date:
        description: start_date 開始時間
        type: string
      weekday:
        description: weekday 曜日  1 平日、2 週末、 3 指定なし
        maximum: 3
        minimum: 0
        type: integer
    required:
    - end_date
    - start_date
    - weekday
    type: object
  request.StartGuideSettingsCreateReq:
    properties:
      autostart_type:
        description: 自動スタート案内基準
        maximum: 1
        minimum: 0
        type: integer
      enable_autostart:
        description: 自動スタート案内表示
        maximum: 1
        minimum: 0
        type: integer
      enable_start_time:
        description: スタート予定時間表示
        maximum: 1
        minimum: 0
        type: integer
      main_text_always:
        description: メイン文言（常時表示）
        maxLength: 30
        type: string
      start_number:
        description: スタートまでの順番
        type: integer
      start_time_schedule:
        description: スタート予定時間
        type: integer
      sub_text_always:
        description: サブ文言（常時表示）
        maxLength: 45
        type: string
      sub_text_auto:
        description: サブ文言（自動案内）
        maxLength: 45
        type: string
    required:
    - autostart_type
    - enable_autostart
    - enable_start_time
    - main_text_always
    - start_number
    - start_time_schedule
    - sub_text_always
    - sub_text_auto
    type: object
  request.Survey:
    properties:
      answer:
        description: answer 回答id
        type: integer
      id:
        description: id question_id
        type: integer
    required:
    - answer
    - id
    type: object
  request.UpdateIndexParams:
    properties:
      id:
        description: id 設問ID
        type: integer
      index:
        description: index 設問順番
        type: integer
    required:
    - id
    - index
    type: object
  response.Answer:
    properties:
      answer:
        description: answer 回答
        type: integer
      id:
        description: id 設問ID
        type: integer
    type: object
  response.AnswerSurvey:
    properties:
      answers:
        description: answers  評価
        items:
          $ref: '#/definitions/response.Evaluation'
        type: array
      id:
        description: id 設問ID
        type: integer
    type: object
  response.BarcodeToLockerResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data データ
        properties:
          locker_no:
            description: 変換後のロッカーキー
            type: string
        type: object
      message:
        description: message メッセージ
        type: string
    type: object
  response.CaddyAnalysisData:
    properties:
      month:
        description: month 年月
        type: string
      survey:
        description: survey キャディ
        items:
          $ref: '#/definitions/response.CaddySurvey'
        type: array
    type: object
  response.CaddyAnalysisResp:
    properties:
      code:
        description: code  コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data データ
        items:
          $ref: '#/definitions/response.CaddyAnalysisData'
        type: array
      message:
        description: message メッセージ
        type: string
    type: object
  response.CaddyData:
    properties:
      caddy_name:
        description: caddy_name キャディ名
        type: string
      caddy_no:
        description: caddy_no キャディ番号
        type: string
      id:
        description: id ID
        type: integer
    type: object
  response.CaddyIndexResp:
    properties:
      code:
        description: code  コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data データ
        items:
          $ref: '#/definitions/response.CaddyData'
        type: array
      message:
        description: message メッセージ
        type: string
    type: object
  response.CaddyOnResp:
    properties:
      caddyon:
        description: caddyon データ
        type: string
      code:
        description: code  コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      message:
        description: message メッセージ
        type: string
      printon:
        description: printon データ
        type: string
    type: object
  response.CaddySurvey:
    properties:
      caddy_id:
        description: caddy_id キャディID
        type: string
      survey:
        description: survey 設問
        items:
          $ref: '#/definitions/response.AnswerSurvey'
        type: array
    type: object
  response.CartInfoData:
    properties:
      caddy_id:
        description: caddy_id キャディID
        type: string
      caddy_name:
        description: caddy_id キャディName
        type: string
      cart_no:
        description: cart_no カート番号
        type: string
      exist_score:
        description: exist_score exist score
        type: boolean
      played_date:
        description: played_date プレイ時間
        type: string
      player:
        description: player プレイヤー
        items:
          $ref: '#/definitions/response.CartPlayer'
        type: array
      serial:
        description: serial for version 2
        type: string
      sort_key:
        description: sort_key sk
        type: string
      start_course:
        description: start_course スタートコース
        type: string
      start_time:
        description: caddy_name スタート時間
        type: string
    type: object
  response.CartInfoResp:
    properties:
      code:
        description: code 简码 正常 0 ， 401 未授权， 500 内部错误
        type: integer
      data:
        description: data 数据
        items:
          $ref: '#/definitions/response.CartInfoData'
        type: array
      message:
        description: message 简述
        type: string
    type: object
  response.CartPlayer:
    properties:
      club_checked:
        description: club_checked クラブ確認済み
        type: boolean
      id:
        description: id プレイヤーID
        type: string
      name:
        description: name プレイヤー名
        type: string
    type: object
  response.Evaluation:
    properties:
      count:
        description: count 回答数
        type: integer
      id:
        description: id 評価ID
        type: integer
    type: object
  response.EvaluationIndexResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data データ
        items:
          $ref: '#/definitions/response.EvaluationRespData'
        type: array
      message:
        description: message メッセージ
        type: string
    type: object
  response.EvaluationRespData:
    properties:
      content:
        description: content 評価文言
        type: string
      id:
        description: id 評価段階
        type: integer
      score:
        description: score 評価配点
        type: integer
      stage:
        description: stage だんかい
        type: integer
    type: object
  response.EvaluationUpdateResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー 500 内部エラー
        type: integer
      data:
        description: data データ
        items:
          $ref: '#/definitions/response.EvaluationRespData'
        type: array
      message:
        description: message メッセージ
        type: string
    type: object
  response.OfficeSettingsCreateResp:
    properties:
      code:
        description: code 简码 正常 0 ， 401 未授权， 500 内部错误
        type: integer
      message:
        description: message 简述
        type: string
    type: object
  response.OfficeSettingsIndexResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data データ
        properties:
          card_reader_type:
            description: カードリード種類　0:なし　1:バーコードリード　2:ICカードリード
            type: integer
          enable_questionnaire:
            description: アンケート機能　0:無効　1:有効
            type: integer
          enable_self_score_print:
            description: セルフスコア印刷機能　0:無効　1:有効
            type: integer
          enable_start_guide:
            description: スタート案内機能　0:無効　1:有効
            type: integer
        type: object
      message:
        description: message メッセージ
        type: string
    type: object
  response.Ping:
    properties:
      aws_config:
        type: boolean
      git_version:
        type: string
      message:
        type: string
    type: object
  response.QuesnaireSettingsCreateResp:
    properties:
      code:
        description: code 简码 正常 0 ， 401 未授权， 500 内部错误
        type: integer
      message:
        description: message 简述
        type: string
    type: object
  response.QuesnaireSettingsIndexResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data データ
        properties:
          caddy_name_type:
            description: キャディの名前種類：1(キャディ)、2(コースアテンダント)
            type: integer
        type: object
      message:
        description: message メッセージ
        type: string
    type: object
  response.QuestionCreateResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー 500 内部エラー
        type: integer
      id:
        description: id リソースid
        type: integer
      message:
        description: message メッセージ
        type: string
    type: object
  response.QuestionDelResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー， 500 内部エラー
        type: integer
      message:
        description: message メッセージ
        type: string
    type: object
  response.QuestionIndexResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data 数据
        items:
          $ref: '#/definitions/response.QuestionRespData'
        type: array
      message:
        description: message メッセージ
        type: string
    type: object
  response.QuestionRespData:
    properties:
      content:
        description: content 設問内容
        type: string
      id:
        description: id 設問ID
        type: integer
      index:
        description: index 設問順番
        type: integer
      require:
        description: require 必須
        type: integer
      type:
        description: type 設問タイプ
        type: integer
    type: object
  response.QuestionShowResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        allOf:
        - $ref: '#/definitions/response.QuestionRespData'
        description: data データ
      message:
        description: message メッセージ
        type: string
    type: object
  response.QuestionnaireCreateResp:
    properties:
      code:
        description: code 简码 正常 0 ， 401 未授权， 500 内部错误
        type: integer
      id:
        description: id 创建资源的id
        type: integer
      message:
        description: message 简述
        type: string
    type: object
  response.QuestionnaireExportCsv:
    properties:
      code:
        description: code 简码 正常 0 ， 401 未授权， 500 内部错误
        type: integer
      message:
        description: message 简述
        type: string
    type: object
  response.QuestionnaireIndexResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      current_page:
        description: current_page 現在ページ
        type: integer
      data:
        description: data データ
        items:
          $ref: '#/definitions/response.QuestionnaireRespData'
        type: array
      from:
        description: from 開始件数
        type: integer
      last_page:
        description: last_page 全ページ数
        type: integer
      message:
        description: message メッセージ
        type: string
      per_page:
        description: per_page １ページ当たり件数
        type: integer
      to:
        description: to 終了件数
        type: integer
      total:
        description: total 条件を満たす件数
        type: integer
    type: object
  response.QuestionnaireRespData:
    properties:
      caddy_id:
        description: caddy_id キャディID
        type: string
      cart_no:
        description: cart_no カート番号
        type: string
      id:
        description: id 主キーID
        type: integer
      played_date:
        description: played_date プレイ時間
        type: string
      start_course:
        description: start_course スタートコース
        type: string
      start_time:
        description: start_Time スタート時間
        type: string
      survey:
        description: survey 設問
        items:
          $ref: '#/definitions/response.Survey'
        type: array
    type: object
  response.StartGuideInfoIndexResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data データ
        properties:
          main_text_always:
            description: メイン文言（常時表示）
            type: string
          main_text_auto:
            description: メイン文言（自動案内）
            type: string
          sub_text_always:
            description: サブ文言（常時表示）
            type: string
          sub_text_auto:
            description: サブ文言（自動案内）
            type: string
        type: object
      message:
        description: message メッセージ
        type: string
    type: object
  response.StartGuideSettingsCreateResp:
    properties:
      code:
        description: code 简码 正常 0 ， 401 未授权， 500 内部错误
        type: integer
      message:
        description: message 简述
        type: string
    type: object
  response.StartGuideSettingsIndexResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data データ
        properties:
          autostart_type:
            description: 自動スタート案内基準
            type: integer
          enable_autostart:
            description: 自動スタート案内表示
            type: integer
          enable_start_time:
            description: スタート予定時間表示
            type: integer
          main_text_always:
            description: メイン文言（常時表示）
            type: string
          start_number:
            description: スタートまでの順番
            type: integer
          start_time_schedule:
            description: スタート予定時間
            type: integer
          sub_text_always:
            description: サブ文言（常時表示）
            type: string
          sub_text_auto:
            description: サブ文言（自動案内）
            type: string
        type: object
      message:
        description: message メッセージ
        type: string
    type: object
  response.StatisticalData:
    properties:
      month:
        description: month 年月
        type: string
      survey:
        description: survey キャディ
        items:
          $ref: '#/definitions/response.AnswerSurvey'
        type: array
    type: object
  response.StatisticalResp:
    properties:
      code:
        description: code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
        type: integer
      data:
        description: data データ
        items:
          $ref: '#/definitions/response.StatisticalData'
        type: array
      message:
        description: message メッセージ
        type: string
    type: object
  response.Survey:
    properties:
      answer_time:
        description: answer_time 回答時間
        type: string
      answers:
        description: answers 回答
        items:
          $ref: '#/definitions/response.Answer'
        type: array
      feedback:
        description: feedback custom comment
        type: string
      feedback_caddy:
        description: キャディのfeedback custom comment
        type: string
      feedback_golf:
        description: ゴルフ場のfeedback custom comment
        type: string
      id:
        description: id プレイヤーID
        type: string
      name:
        description: name プレイヤー名
        type: string
    type: object
  tee.DelegateCompe:
    properties:
      compe_name:
        type: string
      compe_no:
        type: integer
      is_front_system:
        type: boolean
    type: object
  tee.TeeCartData:
    properties:
      cart_no:
        type: integer
      delegate_compe:
        $ref: '#/definitions/tee.DelegateCompe'
      players:
        items:
          $ref: '#/definitions/tee.TeePlayerData'
        type: array
      scheduled_start_time:
        type: string
      start_time:
        type: string
    type: object
  tee.TeeCourseData:
    properties:
      cart_data:
        items:
          $ref: '#/definitions/tee.TeeCartData'
        type: array
      course_index:
        type: string
      course_name:
        type: string
    type: object
  tee.TeeInfo:
    properties:
      men_tee_name:
        type: string
      tee_id:
        type: string
      women_tee_name:
        type: string
    type: object
  tee.TeeInfoResp:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/tee.TeeInfo'
        type: array
      msg:
        type: string
    type: object
  tee.TeePlayer:
    properties:
      birthday:
        description: eg 1942/10/13
        type: string
      cart_no:
        type: integer
      gender:
        description: １：男性、２：女性
        type: integer
      glid_no:
        type: string
      hdcp:
        type: string
      hdcp_index:
        type: string
      locker_no:
        type: string
      office_key:
        type: string
      old_compe_no:
        type: string
      play_date:
        type: string
      player_name:
        type: string
      player_no:
        type: integer
      private_hdcp:
        type: string
      score_hash:
        type: string
      tee_id:
        type: string
      whs_hdcp:
        type: string
    type: object
  tee.TeePlayerData:
    properties:
      birthday:
        type: string
      gender:
        type: integer
      glid_no:
        type: string
      hdcp:
        type: string
      hdcp_index:
        type: string
      joined_compes:
        items:
          properties:
            compe_no:
              type: integer
          type: object
        type: array
      locker_no:
        type: integer
      player_name:
        type: string
      player_no:
        type: integer
      tee_id:
        type: string
    type: object
  tee.TeePlayerResp:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/tee.TeePlayer'
        type: array
      msg:
        type: string
    type: object
  tee.TeeSheetPlayerUpdateReq:
    properties:
      birthday:
        type: string
      cart_no:
        minimum: 1
        type: integer
      gender:
        type: integer
      glid_no:
        type: string
      hdcp:
        type: string
      hdcp_index:
        type: string
      play_date:
        type: string
      player_no:
        minimum: 1
        type: integer
      scheduled_start_time:
        description: should be "hh:mm" or null
        type: string
    required:
    - cart_no
    - play_date
    - player_no
    type: object
  tee.TeeSheetResp:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/tee.TeeCourseData'
        type: array
      msg:
        type: string
    type: object
host: localhost:888
info:
  contact: {}
  description: This is a dev server.
  title: mi-restful-api
  version: "1.0"
paths:
  /app/answer:
    post:
      consumes:
      - application/json
      description: create a score record
      operationId: post-score-store
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.QuestionnaireCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuestionnaireCreateResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuestionnaireCreateResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuestionnaireCreateResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuestionnaireCreateResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuestionnaireCreateResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuestionnaireCreateResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuestionnaireCreateResp'
        "603":
          description: weekday deal error
          schema:
            $ref: '#/definitions/response.QuestionnaireCreateResp'
      summary: 新しい評価を追加
      tags:
      - Questionnaire
  /app/caddyon:
    get:
      description: キャディ評価機能有効
      operationId: get-caddy_on-index
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.CaddyOnResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.CaddyOnResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.CaddyOnResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.CaddyOnResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.CaddyOnResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.CaddyOnResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.CaddyOnResp'
      summary: キャディ評価機能有効
      tags:
      - Caddy
  /app/cartinfo:
    get:
      consumes:
      - application/json
      description: カート情報取得
      operationId: get-cartinfo
      parameters:
      - default: 23
        description: カートID
        in: query
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.CartInfoResp'
      summary: カート情報取得
      tags:
      - Questionnaire
  /app/cartinfo/v2:
    get:
      consumes:
      - application/json
      description: カート情報取得 v2
      operationId: get-cartinfo-v2
      parameters:
      - default: 23
        description: カートID
        in: query
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.CartInfoResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.CartInfoResp'
      summary: カート情報取得 v2
      tags:
      - Questionnaire
  /app/evaluation:
    get:
      description: 評価設置リストを取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
      summary: 評価設置リストを取得
      tags:
      - Evaluation
  /app/office/barcodetolocker:
    get:
      description: バーコードからロッカーキーに変換
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.BarcodeToLockerResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.BarcodeToLockerResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.BarcodeToLockerResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.BarcodeToLockerResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.BarcodeToLockerResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.BarcodeToLockerResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.BarcodeToLockerResp'
      summary: バーコードからロッカーキーに変換
      tags:
      - OfficeSettings
  /app/office/settings:
    get:
      description: システム設定取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
      summary: システム設定取得
      tags:
      - OfficeSettings
  /app/online-compe/:compeNo:
    get:
      description: get compe by no
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
      summary: get compe by no
      tags:
      - OnlineCompe
  /app/online-compe/leaderboard/ranking/:compeNo:
    get:
      description: get leaderboard ranking コンペ一覧・リーダボード
      parameters:
      - description: data str handy or peoria
        in: query
        name: aggregation_type
        required: true
        type: string
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
      summary: get leaderboard ranking
      tags:
      - OnlineCompe
  /app/online-compe/leaderboard/ranking/type/:compeNo:
    get:
      description: get leaderboard ranking コンペ一覧・リーダボード Type
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
      summary: get leaderboard ranking type
      tags:
      - OnlineCompe
  /app/online-compe/player-compe/:playerNo:
    get:
      description: get player compes 参加者 コンペ詳細
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.OnlineCompeJoinedPlayersReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
      summary: get player compes
      tags:
      - OnlineCompe
  /app/quesnairesettings:
    get:
      description: アンケート各種設定取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
      summary: アンケート各種設定取得
      tags:
      - QuesnaireSettings
  /app/question:
    get:
      description: 問題設置リストを取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
      summary: 問題設置リストを取得
      tags:
      - Question
  /app/start-guidance/info:
    get:
      description: スタート案内インフォメーション取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.StartGuideInfoIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.StartGuideInfoIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.StartGuideInfoIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.StartGuideInfoIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.StartGuideInfoIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.StartGuideInfoIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.StartGuideInfoIndexResp'
      summary: スタート案内インフォメーション取得
      tags:
      - StartGuideInfo
  /ping:
    get:
      description: get ping
      operationId: get-string
      produces:
      - application/json
      responses:
        "200":
          description: pong
          schema:
            $ref: '#/definitions/response.Ping'
      summary: server ping
      tags:
      - Ping
  /web/answer/lowest:
    get:
      description: 統計問題の最低点
      operationId: get-answer-lowest
      parameters:
      - default: '"2024-07"'
        description: 開始年月
        in: query
        name: start_month
        required: true
        type: string
      - default: '"2024-07"'
        description: 終了年月
        in: query
        name: end_month
        required: true
        type: string
      - description: キャティID
        in: query
        name: caddy_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.StatisticalResp'
      summary: 統計問題の最低点
      tags:
      - Analysis
  /web/caddy:
    get:
      description: キャディ評価
      operationId: get-answer-caddy
      parameters:
      - default: '"2024-07"'
        description: 開始年月
        in: query
        name: start_month
        required: true
        type: string
      - default: '"2024-07"'
        description: 終了年月
        in: query
        name: end_month
        required: true
        type: string
      - description: キャティID
        in: query
        name: caddy_id
        type: integer
      - default: 3
        description: 曜日[ 1 平日， 2 周末， 3 未指定]
        in: query
        name: weekday
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.CaddyAnalysisResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.CaddyAnalysisResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.CaddyAnalysisResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.CaddyAnalysisResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.CaddyAnalysisResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.CaddyAnalysisResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.CaddyAnalysisResp'
      summary: キャディ評価
      tags:
      - Analysis
  /web/caddylist:
    get:
      description: キャディ一覧
      operationId: get-caddy-index
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.CaddyIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.CaddyIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.CaddyIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.CaddyIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.CaddyIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.CaddyIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.CaddyIndexResp'
      summary: キャディ一覧
      tags:
      - Caddy
  /web/evaluation:
    get:
      description: 評価設置リストを取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.EvaluationIndexResp'
      summary: 評価設置リストを取得
      tags:
      - Evaluation
    put:
      consumes:
      - application/json
      description: update comment
      operationId: put-comment-update
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/request.EvaluationPutReq'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: not config response
          schema:
            $ref: '#/definitions/response.EvaluationUpdateResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.EvaluationUpdateResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.EvaluationUpdateResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.EvaluationUpdateResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.EvaluationUpdateResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.EvaluationUpdateResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.EvaluationUpdateResp'
      summary: 評価内容またスコアを更新
      tags:
      - Evaluation
  /web/office/settings:
    get:
      description: システム設定取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.OfficeSettingsIndexResp'
      summary: システム設定取得
      tags:
      - OfficeSettings
    post:
      consumes:
      - application/json
      description: システム設定保存
      operationId: post-officesettings-store
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.OfficeSettingsCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.OfficeSettingsCreateResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.OfficeSettingsCreateResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.OfficeSettingsCreateResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.OfficeSettingsCreateResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.OfficeSettingsCreateResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.OfficeSettingsCreateResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.OfficeSettingsCreateResp'
      summary: システム設定保存
      tags:
      - OfficeSettings
  /web/online-compe/:compeNo:
    get:
      description: get compe by no
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.OnlineCompeResp'
      summary: get compe by no
      tags:
      - OnlineCompe
  /web/online-compe/compe-player/:compeNo/list:
    get:
      description: get compe player list コンペ詳細・参加者
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/player.JoinedPlayesResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/player.JoinedPlayesResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/player.JoinedPlayesResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/player.JoinedPlayesResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/player.JoinedPlayesResp'
      summary: get compe player list
      tags:
      - OnlineCompe
  /web/online-compe/compe-player/:compeNo/update:
    post:
      description: update compe players コンペ詳細・参加者, also remove player which is not
        in the request
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.OnlineCompeJoinedPlayersReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.BasicResp'
      summary: update compe players
      tags:
      - OnlineCompe
  /web/online-compe/course/list:
    get:
      description: get course list
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/course.CoursesResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/course.CoursesResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/course.CoursesResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/course.CoursesResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/course.CoursesResp'
      summary: get course list
      tags:
      - OnlineCompe
  /web/online-compe/create:
    put:
      description: create compe
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.OnlineCompeCreationReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.BasicResp'
      summary: create compe
      tags:
      - OnlineCompe
  /web/online-compe/default-setting:
    get:
      description: get compe default setting
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.OnlineCompeDefaultSettingResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.OnlineCompeDefaultSettingResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.OnlineCompeDefaultSettingResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.OnlineCompeDefaultSettingResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.OnlineCompeDefaultSettingResp'
      summary: get compe default setting
      tags:
      - OnlineCompe
  /web/online-compe/default-setting/update:
    post:
      description: update compe default setting
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.OnlineCompeDefaultSettingReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.BasicResp'
      summary: update compe default setting
      tags:
      - OnlineCompe
  /web/online-compe/img/upload/:compeNo:
    post:
      description: upload img to s3
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.UploadImgResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.UploadImgResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.UploadImgResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.UploadImgResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.UploadImgResp'
      summary: upload img to s3
      tags:
      - OnlineCompe
  /web/online-compe/join:
    post:
      description: JoinCompe compe
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.OnlineCompePlayerJoinReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.BasicResp'
      summary: JoinCompe compe
      tags:
      - OnlineCompe
  /web/online-compe/latest-no:
    get:
      description: get latest avaliable compe id
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.OnlineCompeLatestNoResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.OnlineCompeLatestNoResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.OnlineCompeLatestNoResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.OnlineCompeLatestNoResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.OnlineCompeLatestNoResp'
      summary: get latest avaliable compe id
      tags:
      - OnlineCompe
  /web/online-compe/leaderboard/ranking/:compeNo:
    get:
      description: get leaderboard ranking コンペ一覧・リーダボード
      parameters:
      - description: data str handy or peoria
        in: query
        name: aggregation_type
        required: true
        type: string
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
      summary: get leaderboard ranking
      tags:
      - OnlineCompe
  /web/online-compe/leaderboard/ranking/share:
    post:
      description: get leaderboard ranking コンペ一覧・リーダボード
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.LeaderboardRankingShareReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.BasicResp'
      summary: get leaderboard ranking
      tags:
      - OnlineCompe
  /web/online-compe/leaderboard/ranking/share-key/create:
    post:
      description: create leaderboard ranking share key
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.LeaderboardRankingShareKeyCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.LeaderboardRankingShareKeyCreateResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.LeaderboardRankingShareKeyCreateResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.LeaderboardRankingShareKeyCreateResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.LeaderboardRankingShareKeyCreateResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.LeaderboardRankingShareKeyCreateResp'
      summary: create leaderboard ranking share key
      tags:
      - OnlineCompe
  /web/online-compe/leaderboard/ranking/shared/:compeNo:
    get:
      description: get leaderboard ranking コンペ一覧・リーダボード
      parameters:
      - description: data str handy or peoria
        in: query
        name: aggregation_type
        required: true
        type: string
      - description: compe no
        in: query
        name: compe_no
        required: true
        type: string
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingResp'
      summary: get leaderboard ranking by share key
      tags:
      - OnlineCompe
  /web/online-compe/leaderboard/ranking/type/:compeNo:
    get:
      description: get leaderboard ranking コンペ一覧・リーダボード Type
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
      summary: get leaderboard ranking type
      tags:
      - OnlineCompe
  /web/online-compe/leaderboard/ranking/type/shared/:compeNo:
    get:
      description: get leaderboard ranking コンペ一覧・リーダボード Type
      parameters:
      - description: compe no
        in: query
        name: compe_no
        required: true
        type: string
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.LeaderBoardRankingTypeResp'
      summary: get leaderboard ranking type by sharekey
      tags:
      - OnlineCompe
  /web/online-compe/office-compe/list:
    get:
      description: get office compe list コンペ一覧
      parameters:
      - description: 競技方法  handy, peoria
        in: query
        name: compe_kind
        type: string
      - description: コンペタイプ 0 team(for phase 2) 1 個人戦
        in: query
        name: compe_type
        type: integer
      - description: key word in the compe name filter by this word
        in: query
        name: free_word
        type: string
      - description: limit more than 100
        in: query
        minimum: 100
        name: limit
        required: true
        type: integer
      - description: オフィスキー
        in: query
        name: office_key
        type: string
      - description: offset >=0
        in: query
        minimum: 0
        name: offset
        required: true
        type: integer
      - description: 開催日 eg. 20060102
        in: query
        name: play_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.OnlineCompeOfficeResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.OnlineCompeOfficeResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.OnlineCompeOfficeResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.OnlineCompeOfficeResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.OnlineCompeOfficeResp'
      summary: get office compe list
      tags:
      - OnlineCompe
  /web/online-compe/player-compe/:playerNo:
    get:
      description: get player compes 参加者 コンペ詳細
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.OnlineCompeJoinedPlayersReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/player.PlayerCompeResp'
      summary: get player compes
      tags:
      - OnlineCompe
  /web/online-compe/player-info/search:
    get:
      description: search player info コンペ詳細・参加者
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.SearchPlayerInfoReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/player.PlayerInfoResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/player.PlayerInfoResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/player.PlayerInfoResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/player.PlayerInfoResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/player.PlayerInfoResp'
      summary: search player info
      tags:
      - OnlineCompe
  /web/online-compe/tee/info:
    get:
      description: get TeeInfo コンペ詳細・参加者
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/tee.TeeInfoResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/tee.TeeInfoResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/tee.TeeInfoResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/tee.TeeInfoResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/tee.TeeInfoResp'
      summary: get TeeInfo
      tags:
      - OnlineCompe
  /web/online-compe/tee/sheet:
    get:
      description: get TeeSheet
      parameters:
      - description: data str like 20200101
        in: query
        name: date_str
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/tee.TeeSheetResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/tee.TeeSheetResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/tee.TeeSheetResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/tee.TeeSheetResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/tee.TeeSheetResp'
      summary: get TeeSheet
      tags:
      - OnlineCompe
  /web/online-compe/tee/sheet/player/search:
    get:
      description: get TeeInfo コンペ詳細・参加者
      parameters:
      - description: birthday 1990-01-01
        in: query
        name: birthday
        required: true
        type: string
      - description: play_date 20230101 , default should be today
        in: query
        name: play_date
        type: string
      - description: PlayerName
        in: query
        name: player_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/tee.TeePlayerResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/tee.TeePlayerResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/tee.TeePlayerResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/tee.TeePlayerResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/tee.TeePlayerResp'
      summary: get player from tee
      tags:
      - OnlineCompe
  /web/online-compe/tee/sheet/player/update:
    post:
      description: update TeeSheet  by date and cart no
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/tee.TeeSheetPlayerUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.BasicResp'
      summary: update TeeSheet by date and cart no
      tags:
      - OnlineCompe
  /web/online-compe/update:
    post:
      description: update compe
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/compe.OnlineCompeUpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/compe.BasicResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/compe.BasicResp'
      summary: update compe
      tags:
      - OnlineCompe
  /web/quesnairesettings:
    get:
      description: アンケート各種設定取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsIndexResp'
      summary: アンケート各種設定取得
      tags:
      - QuesnaireSettings
    post:
      consumes:
      - application/json
      description: create a score record
      operationId: post-quesnairesettings-store
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.QuesnaireSettingsCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsCreateResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsCreateResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsCreateResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsCreateResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsCreateResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsCreateResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuesnaireSettingsCreateResp'
      summary: アンケート各種設定を追加・編集
      tags:
      - QuesnaireSettings
  /web/question:
    get:
      description: 問題設置リストを取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuestionIndexResp'
      summary: 問題設置リストを取得
      tags:
      - Question
    post:
      consumes:
      - application/json
      description: 新しい問題設置を作成
      operationId: get-question-store
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.Question'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuestionCreateResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuestionCreateResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuestionCreateResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuestionCreateResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuestionCreateResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuestionCreateResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuestionCreateResp'
      summary: 新しい問題設置を作成
      tags:
      - Question
  /web/question/:id:
    put:
      consumes:
      - application/json
      description: 問題内容とソートIDを更新
      operationId: put-question-update
      parameters:
      - description: 问题id
        in: path
        name: id
        required: true
        type: string
      - description: 設問内容
        in: body
        name: content
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
      summary: 問題内容を更新
      tags:
      - Question
  /web/question/{id}:
    delete:
      consumes:
      - application/json
      description: delete question
      operationId: get-question-delete
      parameters:
      - description: 設問ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuestionDelResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuestionDelResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuestionDelResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuestionDelResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuestionDelResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuestionDelResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuestionDelResp'
      summary: question delete
      tags:
      - Question
  /web/questionindex:
    put:
      consumes:
      - application/json
      description: 問題内容とソートIDを更新
      operationId: put-question-index
      parameters:
      - description: 設問ID
        in: body
        name: id
        required: true
        schema:
          type: string
      - description: 設問順番
        in: body
        name: index
        required: true
        schema:
          type: string
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.UpdateIndexParams'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuestionShowResp'
      summary: 問題内容を更新
      tags:
      - Question
  /web/questionnaire:
    get:
      description: 評価リスト
      operationId: get-score-list
      parameters:
      - description: Request json data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.QuestionnaireIndexReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.QuestionnaireIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuestionnaireIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuestionnaireIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuestionnaireIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuestionnaireIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuestionnaireIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuestionnaireIndexResp'
      summary: 評価リスト
      tags:
      - Questionnaire
  /web/questionnaire/csv:
    get:
      consumes:
      - application/json
      description: create a score record
      operationId: get-questionnaire-csv
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            type: string
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.QuestionnaireExportCsv'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.QuestionnaireExportCsv'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.QuestionnaireExportCsv'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.QuestionnaireExportCsv'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.QuestionnaireExportCsv'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.QuestionnaireExportCsv'
        "604":
          description: csv header write error
          schema:
            $ref: '#/definitions/response.QuestionnaireExportCsv'
        "605":
          description: csv data write error
          schema:
            $ref: '#/definitions/response.QuestionnaireExportCsv'
      summary: 新しい評価を追加
      tags:
      - Questionnaire
  /web/start-guidance/settings:
    get:
      description: スタート案内設定取得
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.StartGuideSettingsIndexResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.StartGuideSettingsIndexResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.StartGuideSettingsIndexResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.StartGuideSettingsIndexResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.StartGuideSettingsIndexResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.StartGuideSettingsIndexResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.StartGuideSettingsIndexResp'
      summary: スタート案内設定取得
      tags:
      - StartGuideSettings
    post:
      consumes:
      - application/json
      description: スタート案内設定を保存
      operationId: post-startguidesettings-store
      parameters:
      - description: Request payload
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/request.StartGuideSettingsCreateReq'
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.StartGuideSettingsCreateResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.StartGuideSettingsCreateResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.StartGuideSettingsCreateResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.StartGuideSettingsCreateResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.StartGuideSettingsCreateResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.StartGuideSettingsCreateResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.StartGuideSettingsCreateResp'
      summary: スタート案内設定を保存
      tags:
      - StartGuideSettings
  /web/statistical:
    get:
      description: アンケート統計
      operationId: get-answer-statistical
      parameters:
      - default: '"2024-07"'
        description: 開始年月
        in: query
        name: start_month
        required: true
        type: string
      - default: '"2024-07"'
        description: 終了年月
        in: query
        name: end_month
        required: true
        type: string
      - description: キャティID
        in: query
        name: caddy_id
        type: integer
      - default: 3
        description: 曜日[ 1 平日， 2 周末， 3 未指定]
        in: query
        name: weekday
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: response
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "400":
          description: Bad Request ,param error
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "601":
          description: mysql conn error
          schema:
            $ref: '#/definitions/response.StatisticalResp'
        "602":
          description: mysql sql error
          schema:
            $ref: '#/definitions/response.StatisticalResp'
      summary: アンケート統計
      tags:
      - Analysis
swagger: "2.0"
