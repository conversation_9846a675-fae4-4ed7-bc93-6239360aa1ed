package start_guidance

type PlayerTeeTime struct {
	CourseName   string        `json:"course_name"`
	CourseIndex  string        `json:"course_index"`
	LockerNo     string        `json:"locker_no"`
	Time         string        `json:"time"`
	Status       string        `json:"status"`
	CartNo       string        `json:"cart_no"`
	PlayerName   string        `json:"player_name"`
	OtherPlayers []OtherPlayer `json:"other_players"`
}

type OtherPlayer struct {
	LockerNumber string `json:"locker_no"`
	PlayerNo     string `json:"player_no"`
	PlayerName   string `json:"player_name"`
	CheckedIn    bool   `json:"checked_in"`
}

type TeeTimesData struct {
	CurrentTime string          `json:"current_time"`
	TeeCourse   []TeeCourseData `json:"tee_course"`
	Information string          `json:"information"`
}

type TeeCourseData struct {
	CourseName  string    `json:"course_name"`
	CourseIndex string    `json:"course_index"`
	TeeTimes    []TeeTime `json:"tee_times"`
}

type TeeTime struct {
	No      string   `json:"no"`
	Time    string   `json:"time"`
	Status  string   `json:"status"`
	CartNo  string   `json:"cart_no"`
	Players []Player `json:"players"`
}

type Player struct {
	LockerNo   string `json:"locker_no"`
	PlayerNo   string `json:"player_no"`
	PlayerName string `json:"player_name"`
}

type LockerCartInfo struct {
	CartNo  string   `json:"cart_no"`
	Players []Player `json:"players"`
}
